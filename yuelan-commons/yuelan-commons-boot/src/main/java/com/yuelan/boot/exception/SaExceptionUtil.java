package com.yuelan.boot.exception;

import cn.dev33.satoken.exception.*;
import cn.hutool.core.util.StrUtil;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;

public class SaExceptionUtil {

    public static BizResult handler(SaTokenException ex) {
        if (ex instanceof NotLoginException) {
//            String msg = StrUtil.subBefore(ex.getMessage(), "：", false);
//            return BizResult.error(AuthErrorCodeEnum.RELOGIN, msg);
            return notLoginException((NotLoginException) ex);
        } else if (ex instanceof NotRoleException) {
            return BizResult.error(BaseErrorCodeEnum.NOT_ROLE, ex.getMessage());
        } else if (ex instanceof NotPermissionException) {
            return BizResult.error(BaseErrorCodeEnum.NOT_PERMISSION, ex.getMessage());
        } else if (ex instanceof DisableServiceException) {
            DisableServiceException disableServiceException = (DisableServiceException) ex;
            return BizResult.error(BaseErrorCodeEnum.UNAUTHORIZED, "账号被封禁：" + disableServiceException.getDisableTime() + "秒后解封");
        } else {
            String msg = StrUtil.subBefore(ex.getMessage(), "：", false);
            return BizResult.error(BaseErrorCodeEnum.AUTH_FAIL, msg);
        }
    }


    private static BizResult notLoginException(NotLoginException ex) {
        String message = "";
        if (ex.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "未能读取到有效 token";
        } else if (ex.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "token 无效";
        } else if (ex.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "token 已过期";
        } else if (ex.getType().equals(NotLoginException.BE_REPLACED)) {
            message = "token 已被顶下线";
        } else if (ex.getType().equals(NotLoginException.KICK_OUT)) {
            message = "token 已被踢下线";
        } else if (ex.getType().equals(NotLoginException.TOKEN_FREEZE)) {
            message = "token 已被冻结";
        } else if (ex.getType().equals(NotLoginException.NO_PREFIX)) {
            message = "未按照指定前缀提交 token";
        } else {
            message = "当前会话未登录";
        }
        return BizResult.error(BaseErrorCodeEnum.RELOGIN, message);
    }
}
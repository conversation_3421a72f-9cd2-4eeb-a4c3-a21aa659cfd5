package com.yuelan.boot.constant;

import cn.hutool.extra.spring.SpringUtil;

public class AppConstants {
    /**
     * 应用名称
     */
    public static String getApplicationName() {
        return SpringUtil.getApplicationName();
    }

    /**
     * 线上环境
     */
    public static boolean isReal() {
        return SpringUtil.getApplicationContext().getEnvironment().matchesProfiles("real");
    }

    /**
     * 开发环境
     */
    public static boolean isDev() {
        return SpringUtil.getApplicationContext().getEnvironment().matchesProfiles("dev");
    }

    /**
     * 本地环境
     */
    public static boolean isLocal() {
        return SpringUtil.getApplicationContext().getEnvironment().matchesProfiles("local");
    }


}

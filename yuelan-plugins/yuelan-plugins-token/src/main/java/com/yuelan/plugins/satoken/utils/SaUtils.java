package com.yuelan.plugins.satoken.utils;

import cn.dev33.satoken.session.SaSession;
import com.alibaba.fastjson2.JSONObject;

import java.util.Objects;

public class SaUtils {

    /**
     * 用户信息
     */
    public static final String USER_INFO_KEY = "userinfo";


    public static <T> T getUserInfo(SaSession saSession, Class<T> t) {
        if (Objects.isNull(saSession)) {
            return null;
        }
        JSONObject userInfo = (JSONObject) saSession.get(USER_INFO_KEY);
        if (Objects.isNull(userInfo)) {
            return null;
        }
        return userInfo.to(t);
    }

    public static void setUserInfo(SaSession saSession, Object value) {
        if (Objects.isNull(saSession)) {
            return;
        }
        saSession.set(USER_INFO_KEY, value);
    }

    public static long paresLoginIdAsLong(Object loginId) {
        if (loginId instanceof Long) {
            return (Long) loginId;
        }
        return Long.parseLong(String.valueOf(loginId));
    }
}

package com.yuelan.plugins.log.aspect;

import com.alibaba.fastjson2.JSON;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.context.LogContext;
import com.yuelan.plugins.log.enums.OperationStatus;
import com.yuelan.plugins.log.filter.PropertyPreExcludeFilter;
import com.yuelan.plugins.log.listener.LogListener;
import com.yuelan.plugins.log.listener.LogListenerHelper;
import com.yuelan.plugins.log.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.HttpMethod;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 操作日志记录处理
 */
@Slf4j
@Aspect
public class LogAspect {
    /** 排除敏感属性字段 */
    public static final String[] EXCLUDE_PROPERTIES = {"password", "oldPassword", "newPassword", "confirmPassword"};
    @Resource
    protected HttpServletRequest request;
    @Resource
    private LogListenerHelper helper;

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
        handleLog(joinPoint, controllerLog, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult) {
        try {
            // *========记录日志=========*//
            LogContext logContext = new LogContext();
            if (e == null) {
                logContext.setStatus(OperationStatus.SUCCESS);
            } else {
                logContext.setStatus(OperationStatus.FAIL);
                logContext.setMessage(e.getMessage());
            }
            //请求地址
            logContext.setRequestURI(LogUtil.getRequestURI(request));
            //请求方式
            logContext.setRequestMethod(LogUtil.getRequestMethod(request));
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, logContext, jsonResult);
            // 保存日志
            LogListener logListener = helper.getListener(controllerLog.login());
            logListener.listener(logContext, request);
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("用户操作日志入库异常。", exp);
        }
    }


    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, LogContext logContext, Object jsonResult) throws Exception {
        // 设置action动作
        logContext.setType(log.type());
        // 设置标题
        logContext.setTitle(log.title());
        //账号体系标识
        logContext.setLogin(log.login());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            logContext.setRequestData(getRequestValue(joinPoint, logContext));
        }
        // 是否需要保存response，参数和值
        if (log.isSaveResponseData() && Objects.nonNull(jsonResult)) {
            logContext.setResponseData(JSON.toJSONString(jsonResult));
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @throws Exception 异常
     */
    private String getRequestValue(JoinPoint joinPoint, LogContext logContext) throws Exception {
        String requestMethod = logContext.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            return argsArrayToString(joinPoint.getArgs());
        } else {
            //GET
            return "";
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (Objects.nonNull(o) && !isFilterObject(o)) {
                    try {
                        String jsonObj = JSON.toJSONString(o, excludePropertyPreFilter());
                        params += jsonObj + " ";
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return params.trim();
    }

    /**
     * 忽略敏感属性
     */
    public PropertyPreExcludeFilter excludePropertyPreFilter() {
        return new PropertyPreExcludeFilter().addExcludes(EXCLUDE_PROPERTIES);
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.entrySet()) {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}

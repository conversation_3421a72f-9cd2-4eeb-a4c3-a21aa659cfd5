package com.yuelan.plugins.log.utils;

import cn.hutool.extra.servlet.ServletUtil;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletRequest;


public class LogUtil {

    public static String getRequestMethod(HttpServletRequest request) {
        return request.getMethod();
    }

    public static String getRequestURI(HttpServletRequest request) {
        return request.getRequestURI();
    }

    public static String getIpAddr(HttpServletRequest request) {
        return ServletUtil.getClientIP(request);
    }

    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader(HttpHeaders.USER_AGENT);
    }

    public static String getToken(HttpServletRequest request, String tokenName) {
        return request.getHeader(tokenName);
    }
}
